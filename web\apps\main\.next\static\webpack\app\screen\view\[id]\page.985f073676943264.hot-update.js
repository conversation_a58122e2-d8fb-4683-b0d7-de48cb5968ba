"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/screen/components/GovernmentDataOverview.tsx":
/*!**************************************************************!*\
  !*** ./src/app/screen/components/GovernmentDataOverview.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernmentDataOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _modules_DataSummaryModule__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./modules/DataSummaryModule */ \"(app-pages-browser)/./src/app/screen/components/modules/DataSummaryModule.tsx\");\n/* harmony import */ var _modules_PopulationModule__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./modules/PopulationModule */ \"(app-pages-browser)/./src/app/screen/components/modules/PopulationModule.tsx\");\n/* harmony import */ var _modules_EconomicModule__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./modules/EconomicModule */ \"(app-pages-browser)/./src/app/screen/components/modules/EconomicModule.tsx\");\n/* harmony import */ var _modules_ServiceModule__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./modules/ServiceModule */ \"(app-pages-browser)/./src/app/screen/components/modules/ServiceModule.tsx\");\n/* harmony import */ var _modules_GeographicModule__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./modules/GeographicModule */ \"(app-pages-browser)/./src/app/screen/components/modules/GeographicModule.tsx\");\n/* harmony import */ var _modules_TrendAnalysisModule__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./modules/TrendAnalysisModule */ \"(app-pages-browser)/./src/app/screen/components/modules/TrendAnalysisModule.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction GovernmentDataOverview() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 确保组件已挂载后再显示时间\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setCurrentTime(new Date());\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 text-white overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-800/30 to-cyan-800/30 backdrop-blur-sm border-b border-blue-500/20 px-8 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                    children: \"政务数据总览大屏\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-200 mt-1\",\n                                    children: \"Government Data Overview Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-mono text-blue-300\",\n                                    children: mounted && currentTime ? currentTime.toLocaleTimeString(\"zh-CN\", {\n                                        hour12: false\n                                    }) : \"--:--:--\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-blue-200\",\n                                    children: mounted && currentTime ? currentTime.toLocaleDateString(\"zh-CN\", {\n                                        year: \"numeric\",\n                                        month: \"long\",\n                                        day: \"numeric\",\n                                        weekday: \"long\"\n                                    }) : \"加载中...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 h-[calc(100%-100px)] grid grid-cols-12 grid-rows-8 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-12 row-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modules_DataSummaryModule__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 row-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modules_PopulationModule__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 row-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modules_EconomicModule__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 row-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modules_ServiceModule__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-8 row-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modules_GeographicModule__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-4 row-span-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_modules_TrendAnalysisModule__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\GovernmentDataOverview.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(GovernmentDataOverview, \"H0SuvNzTigWCYXKC7/QNi4Y9i1Y=\");\n_c = GovernmentDataOverview;\nvar _c;\n$RefreshReg$(_c, \"GovernmentDataOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/components/GovernmentDataOverview.tsx\n"));

/***/ })

});