"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/screen/components/modules/PopulationModule.tsx":
/*!****************************************************************!*\
  !*** ./src/app/screen/components/modules/PopulationModule.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PopulationModule; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PopulationModule() {\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        ageGroups: [\n            {\n                name: \"0-18岁\",\n                value: 234567,\n                percentage: 18.8\n            },\n            {\n                name: \"19-35岁\",\n                value: 456789,\n                percentage: 36.7\n            },\n            {\n                name: \"36-60岁\",\n                value: 423456,\n                percentage: 34.0\n            },\n            {\n                name: \"60岁以上\",\n                value: 130866,\n                percentage: 10.5\n            }\n        ],\n        genderRatio: {\n            male: 52.3,\n            female: 47.7\n        },\n        educationLevel: [\n            {\n                level: \"本科及以上\",\n                count: 345678,\n                percentage: 27.8\n            },\n            {\n                level: \"专科\",\n                count: 234567,\n                percentage: 18.8\n            },\n            {\n                level: \"高中\",\n                count: 456789,\n                percentage: 36.7\n            },\n            {\n                level: \"初中及以下\",\n                count: 208644,\n                percentage: 16.7\n            }\n        ]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = ()=>{\n            setData({\n                ageGroups: [\n                    {\n                        name: \"0-18岁\",\n                        value: 234567,\n                        percentage: 18.8\n                    },\n                    {\n                        name: \"19-35岁\",\n                        value: 456789,\n                        percentage: 36.7\n                    },\n                    {\n                        name: \"36-60岁\",\n                        value: 423456,\n                        percentage: 34.0\n                    },\n                    {\n                        name: \"60岁以上\",\n                        value: 130866,\n                        percentage: 10.5\n                    }\n                ],\n                genderRatio: {\n                    male: 52.3,\n                    female: 47.7\n                },\n                educationLevel: [\n                    {\n                        level: \"本科及以上\",\n                        count: 345678,\n                        percentage: 27.8\n                    },\n                    {\n                        level: \"专科\",\n                        count: 234567,\n                        percentage: 18.8\n                    },\n                    {\n                        level: \"高中\",\n                        count: 456789,\n                        percentage: 36.7\n                    },\n                    {\n                        level: \"初中及以下\",\n                        count: 208644,\n                        percentage: 16.7\n                    }\n                ]\n            });\n        };\n        const interval = setInterval(loadData, 60000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gradient-to-br from-blue-800/20 to-blue-900/20 backdrop-blur-sm rounded-2xl border border-blue-700/30 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-5 h-5 text-blue-400\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white\",\n                        children: \"人口统计\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4 h-[calc(100%-60px)] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-blue-300 mb-3\",\n                                children: \"年龄分布\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: data.ageGroups.map((group, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300\",\n                                                children: group.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 flex-1 mx-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 bg-gray-700 rounded-full h-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full transition-all duration-1000\",\n                                                            style: {\n                                                                width: \"\".concat(group.percentage, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-blue-300 w-8\",\n                                                        children: [\n                                                            group.percentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, group.name, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-blue-300 mb-3\",\n                                children: \"性别比例\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: [\n                                                    data.genderRatio.male,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"男性\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-full flex items-center justify-center mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"w-8 h-8 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-bold text-white\",\n                                                children: [\n                                                    data.genderRatio.female,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"女性\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-blue-300 mb-3\",\n                                children: \"教育水平\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: data.educationLevel.map((edu, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300\",\n                                                children: edu.level\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300\",\n                                                        children: edu.count.toLocaleString()\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: [\n                                                            \"(\",\n                                                            edu.percentage,\n                                                            \"%)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, edu.level, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\PopulationModule.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(PopulationModule, \"qT18sfRAtqHBMFDS8A8CgGfZl94=\");\n_c = PopulationModule;\nvar _c;\n$RefreshReg$(_c, \"PopulationModule\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/components/modules/PopulationModule.tsx\n"));

/***/ })

});