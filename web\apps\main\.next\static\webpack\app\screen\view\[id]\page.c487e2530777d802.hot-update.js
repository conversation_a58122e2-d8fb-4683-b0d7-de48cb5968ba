"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/screen/view/[id]/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/screen/view/[id]/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ScreenViewPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize2,Minimize2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize2,Minimize2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize2,Minimize2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize2,Minimize2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// 动态导入组件以避免SSR问题\nconst GovernmentDataOverview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_c = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_screen_components_GovernmentDataOverview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/GovernmentDataOverview */ \"(app-pages-browser)/./src/app/screen/components/GovernmentDataOverview.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\screen\\\\view\\\\[id]\\\\page.tsx -> \" + \"../../components/GovernmentDataOverview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: \"加载大屏数据中...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 13,\n            columnNumber: 5\n        }, undefined)\n});\n_c1 = GovernmentDataOverview;\nfunction ScreenViewPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAutoRefresh, setIsAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // 根据ID获取对应的大屏配置\n    const getScreenConfig = (id)=>{\n        const configs = {\n            \"1\": {\n                name: \"政务数据总览\",\n                component: GovernmentDataOverview,\n                refreshInterval: 30000 // 30秒刷新一次\n            },\n            \"2\": {\n                name: \"人口统计分析\",\n                component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full bg-gray-900 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white mb-2\",\n                                    children: \"人口统计分析大屏\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"功能开发中，敬请期待...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this),\n                refreshInterval: 60000\n            },\n            \"3\": {\n                name: \"经济运行监控\",\n                component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full bg-gray-900 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDCC8\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white mb-2\",\n                                    children: \"经济运行监控大屏\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"功能开发中，敬请期待...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                refreshInterval: 30000\n            },\n            \"4\": {\n                name: \"环境质量监测\",\n                component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-full bg-gray-900 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-6xl mb-4\",\n                                    children: \"\\uD83C\\uDF31\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white mb-2\",\n                                    children: \"环境质量监测大屏\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400\",\n                                    children: \"功能开发中，敬请期待...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                refreshInterval: 15000\n            }\n        };\n        return configs[id];\n    };\n    const screenConfig = getScreenConfig(params.id);\n    const ScreenComponent = screenConfig === null || screenConfig === void 0 ? void 0 : screenConfig.component;\n    // 全屏切换\n    const toggleFullscreen = ()=>{\n        if (!isFullscreen) {\n            document.documentElement.requestFullscreen();\n        } else {\n            document.exitFullscreen();\n        }\n        setIsFullscreen(!isFullscreen);\n    };\n    // 监听全屏状态变化\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleFullscreenChange = ()=>{\n            setIsFullscreen(!!document.fullscreenElement);\n        };\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n        return ()=>document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n    }, []);\n    // 自动刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!isAutoRefresh || !screenConfig) return;\n        const interval = setInterval(()=>{\n            // 触发数据刷新\n            window.location.reload();\n        }, screenConfig.refreshInterval);\n        return ()=>clearInterval(interval);\n    }, [\n        isAutoRefresh,\n        screenConfig\n    ]);\n    if (!screenConfig) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"大屏不存在\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/screen\",\n                        className: \"text-blue-400 hover:text-blue-300 transition-colors\",\n                        children: \"返回大屏列表\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 \".concat(isFullscreen ? \"p-0\" : \"px-4 py-2\"),\n        children: [\n            !isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4 bg-gray-800/50 backdrop-blur-sm rounded-lg px-4 py-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/screen\",\n                                className: \"text-gray-400 hover:text-white transition-colors flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"返回\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: screenConfig.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsAutoRefresh(!isAutoRefresh),\n                                className: \"p-2 rounded-lg transition-colors \".concat(isAutoRefresh ? \"bg-green-600 text-white\" : \"bg-gray-700 text-gray-400 hover:text-white\"),\n                                title: isAutoRefresh ? \"关闭自动刷新\" : \"开启自动刷新\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isAutoRefresh ? \"animate-spin\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleFullscreen,\n                                className: \"p-2 bg-gray-700 text-gray-400 hover:text-white rounded-lg transition-colors\",\n                                title: \"全屏显示\",\n                                children: isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 31\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 67\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isFullscreen ? \"h-screen\" : \"min-h-[calc(100vh-120px)]\"),\n                children: ScreenComponent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenComponent, {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 29\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s(ScreenViewPage, \"S7yWC8Bl7SXvSjlkSm+5PYnRWpI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams\n    ];\n});\n_c2 = ScreenViewPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"GovernmentDataOverview$dynamic\");\n$RefreshReg$(_c1, \"GovernmentDataOverview\");\n$RefreshReg$(_c2, \"ScreenViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/view/[id]/page.tsx\n"));

/***/ })

});