"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js":
/*!***********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js ***!
  \***********************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9hcGkvYXBwLWR5bmFtaWMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBDO0FBQ1U7O0FBRXBEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9hcGkvYXBwLWR5bmFtaWMuanM/YWRkZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi4vc2hhcmVkL2xpYi9hcHAtZHluYW1pY1wiO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gXCIuLi9zaGFyZWQvbGliL2FwcC1keW5hbWljXCI7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1keW5hbWljLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/screen/view/[id]/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/screen/view/[id]/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ScreenViewPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize2,Minimize2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize2,Minimize2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize2,Minimize2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Maximize2,Minimize2,RefreshCw!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// 动态导入组件以避免SSR问题\nconst GovernmentDataOverview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_c = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_app_screen_components_GovernmentDataOverview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/GovernmentDataOverview */ \"(app-pages-browser)/./src/app/screen/components/GovernmentDataOverview.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\screen\\\\view\\\\[id]\\\\page.tsx -> \" + \"../../components/GovernmentDataOverview\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex items-center justify-center bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white\",\n                        children: \"加载大屏数据中...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 13,\n            columnNumber: 5\n        }, undefined)\n});\n_c1 = GovernmentDataOverview;\nfunction ScreenViewPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isAutoRefresh, setIsAutoRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // 根据ID获取对应的大屏配置\n    const getScreenConfig = (id)=>{\n        const configs = {\n            \"1\": {\n                name: \"政务数据总览\",\n                component: GovernmentDataOverview,\n                refreshInterval: 30000 // 30秒刷新一次\n            },\n            \"2\": {\n                name: \"人口统计分析\",\n                component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"人口统计分析大屏 - 开发中\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 26\n                    }, this),\n                refreshInterval: 60000\n            },\n            \"3\": {\n                name: \"经济运行监控\",\n                component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"经济运行监控大屏 - 开发中\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 26\n                    }, this),\n                refreshInterval: 30000\n            },\n            \"4\": {\n                name: \"环境质量监测\",\n                component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"环境质量监测大屏 - 开发中\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 26\n                    }, this),\n                refreshInterval: 15000\n            }\n        };\n        return configs[id];\n    };\n    const screenConfig = getScreenConfig(params.id);\n    const ScreenComponent = screenConfig === null || screenConfig === void 0 ? void 0 : screenConfig.component;\n    // 全屏切换\n    const toggleFullscreen = ()=>{\n        if (!isFullscreen) {\n            document.documentElement.requestFullscreen();\n        } else {\n            document.exitFullscreen();\n        }\n        setIsFullscreen(!isFullscreen);\n    };\n    // 监听全屏状态变化\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleFullscreenChange = ()=>{\n            setIsFullscreen(!!document.fullscreenElement);\n        };\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n        return ()=>document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n    }, []);\n    // 自动刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!isAutoRefresh || !screenConfig) return;\n        const interval = setInterval(()=>{\n            // 触发数据刷新\n            window.location.reload();\n        }, screenConfig.refreshInterval);\n        return ()=>clearInterval(interval);\n    }, [\n        isAutoRefresh,\n        screenConfig\n    ]);\n    if (!screenConfig) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"大屏不存在\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/screen\",\n                        className: \"text-blue-400 hover:text-blue-300 transition-colors\",\n                        children: \"返回大屏列表\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-900 \".concat(isFullscreen ? \"p-0\" : \"px-4 py-2\"),\n        children: [\n            !isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4 bg-gray-800/50 backdrop-blur-sm rounded-lg px-4 py-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/screen\",\n                                className: \"text-gray-400 hover:text-white transition-colors flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"返回\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: screenConfig.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsAutoRefresh(!isAutoRefresh),\n                                className: \"p-2 rounded-lg transition-colors \".concat(isAutoRefresh ? \"bg-green-600 text-white\" : \"bg-gray-700 text-gray-400 hover:text-white\"),\n                                title: isAutoRefresh ? \"关闭自动刷新\" : \"开启自动刷新\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isAutoRefresh ? \"animate-spin\" : \"\")\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleFullscreen,\n                                className: \"p-2 bg-gray-700 text-gray-400 hover:text-white rounded-lg transition-colors\",\n                                title: \"全屏显示\",\n                                children: isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 31\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Maximize2_Minimize2_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 67\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isFullscreen ? \"h-screen\" : \"min-h-[calc(100vh-120px)]\"),\n                children: ScreenComponent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenComponent, {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 29\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\view\\\\[id]\\\\page.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(ScreenViewPage, \"S7yWC8Bl7SXvSjlkSm+5PYnRWpI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams\n    ];\n});\n_c2 = ScreenViewPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"GovernmentDataOverview$dynamic\");\n$RefreshReg$(_c1, \"GovernmentDataOverview\");\n$RefreshReg$(_c2, \"ScreenViewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/view/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js":
/*!******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \******************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/../../node_modules/.pnpm/@swc+helpers@0.5.5/node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    var _mergedOptions_loadableGenerated;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    const mergedOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    return (0, _loadable.default)({\n        ...mergedOptions,\n        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBaUNBOzs7ZUFBd0JBOzs7Ozs0RUFqQ047K0VBQ0c7QUFnQ04sU0FBU0EsUUFDdEJDLGNBQTZDLEVBQzdDQyxPQUEyQjtRQW1DaEJDO0lBakNYLElBQUlDLGtCQUFzQztRQUN4Qyx3REFBd0Q7UUFDeERDLFNBQVMsQ0FBQUM7Z0JBQUMsRUFBRUMsS0FBSyxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRSxHQUFBSDtZQUN2QyxJQUFJLENBQUNHLFdBQVcsT0FBTztZQUN2QixJQUFJQyxJQUF5QixFQUFjO2dCQUN6QyxJQUFJRixXQUFXO29CQUNiLE9BQU87Z0JBQ1Q7Z0JBQ0EsSUFBSUQsT0FBTztvQkFDVCxPQUNFLFdBREYsR0FDRSxJQUFBSSxZQUFBQyxJQUFBLEVBQUNDLEtBQUFBOzs0QkFDRU4sTUFBTU8sT0FBTzswQ0FDZCxJQUFBSCxZQUFBSSxHQUFBLEVBQUNDLE1BQUFBLENBQUFBOzRCQUNBVCxNQUFNVSxLQUFLOzs7Z0JBR2xCO1lBQ0Y7WUFDQSxPQUFPO1FBQ1Q7SUFDRjtJQUVBLElBQUksT0FBT2hCLG1CQUFtQixZQUFZO1FBQ3hDRyxnQkFBZ0JjLE1BQU0sR0FBR2pCO0lBQzNCO0lBRUEsTUFBTUUsZ0JBQWdCO1FBQ3BCLEdBQUdDLGVBQWU7UUFDbEIsR0FBR0YsT0FBTztJQUNaO0lBRUEsT0FBT2lCLENBQUFBLEdBQUFBLFVBQUFBLE9BQVEsRUFBQztRQUNkLEdBQUdoQixhQUFhO1FBQ2hCaUIsU0FBTyxDQUFFakIsbUNBQUFBLGNBQWNrQixpQkFBaUIscUJBQS9CbEIsaUNBQWlDaUIsT0FBTztJQUNuRDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9hcHAtZHluYW1pYy50c3g/YzU2NSJdLCJuYW1lcyI6WyJkeW5hbWljIiwiZHluYW1pY09wdGlvbnMiLCJvcHRpb25zIiwibWVyZ2VkT3B0aW9ucyIsImxvYWRhYmxlT3B0aW9ucyIsImxvYWRpbmciLCJwYXJhbSIsImVycm9yIiwiaXNMb2FkaW5nIiwicGFzdERlbGF5IiwicHJvY2VzcyIsIl9qc3hydW50aW1lIiwianN4cyIsInAiLCJtZXNzYWdlIiwianN4IiwiYnIiLCJzdGFjayIsImxvYWRlciIsIkxvYWRhYmxlIiwibW9kdWxlcyIsImxvYWRhYmxlR2VuZXJhdGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \******************************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (typeof window === \"undefined\") {\n        throw new _bailouttocsr.BailoutToCSRError(reason);\n    }\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2xhenktZHluYW1pYy9keW5hbWljLWJhaWxvdXQtdG8tY3NyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFjTyxNQUFBQSxnQkFBc0JDLG1CQUFBQSxDQUF1QztTQUF2Q0MsYUFBVUMsS0FBUTtJQUM3QyxJQUFJLEVBQUFDLE1BQU9DLEVBQUFBLFFBQVcsS0FBQUM7UUFDcEIsT0FBTUQsV0FBSUUsYUFBQUE7UUFDWixVQUFBUCxjQUFBTyxpQkFBQSxDQUFBSDtJQUVBO0lBQ0YsT0FBQUQ7O0tBTjZCRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvbGF6eS1keW5hbWljL2R5bmFtaWMtYmFpbG91dC10by1jc3IudHN4PzRmZjQiXSwibmFtZXMiOlsiX2JhaWxvdXR0b2NzciIsInJlcXVpcmUiLCJCYWlsb3V0VG9DU1IiLCJjaGlsZHJlbiIsInJlYXNvbiIsIndpbmRvdyIsInBhcmFtIiwiQmFpbG91dFRvQ1NSRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \****************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\nconst _preloadcss = __webpack_require__(/*! ./preload-css */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n    // Cases:\n    // mod: { default: Component }\n    // mod: Component\n    // mod: { $$typeof, default: proxy(Component) }\n    // mod: proxy(Component)\n    const hasDefault = mod && \"default\" in mod;\n    return {\n        default: hasDefault ? mod.default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                typeof window === \"undefined\" ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_preloadcss.PreloadCss, {\n                    moduleIds: opts.modules\n                }) : null,\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                    ...props\n                })\n            ]\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: fallbackElement,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js ***!
  \*******************************************************************************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PreloadCss\", ({\n    enumerable: true,\n    get: function() {\n        return PreloadCss;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _requestasyncstorageexternal = __webpack_require__(/*! ../../../client/components/request-async-storage.external */ \"(shared)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/request-async-storage.external.js\");\nfunction PreloadCss(param) {\n    let { moduleIds } = param;\n    // Early return in client compilation and only load requestStore on server side\n    if (typeof window !== \"undefined\") {\n        return null;\n    }\n    const requestStore = (0, _requestasyncstorageexternal.getExpectedRequestStore)(\"next/dynamic css\");\n    const allFiles = [];\n    // Search the current dynamic call unique key id in react loadable manifest,\n    // and find the corresponding CSS files to preload\n    if (requestStore.reactLoadableManifest && moduleIds) {\n        const manifest = requestStore.reactLoadableManifest;\n        for (const key of moduleIds){\n            if (!manifest[key]) continue;\n            const cssFiles = manifest[key].files.filter((file)=>file.endsWith(\".css\"));\n            allFiles.push(...cssFiles);\n        }\n    }\n    if (allFiles.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: allFiles.map((file)=>{\n            return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                // @ts-ignore\n                precedence: \"dynamic\",\n                rel: \"stylesheet\",\n                href: requestStore.assetPrefix + \"/_next/\" + encodeURI(file),\n                as: \"style\"\n            }, file);\n        })\n    });\n} //# sourceMappingURL=preload-css.js.map\n_c = PreloadCss;\nvar _c;\n$RefreshReg$(_c, \"PreloadCss\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9zaGFyZWQvbGliL2xhenktZHluYW1pYy9wcmVsb2FkLWNzcy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlPLE1BQUFBLCtCQUFzRUMsbUJBQUFBLENBQUE7U0FBbERDLFdBQVdDLEtBQVg7SUFDekIsTUFBQUMsU0FBQSxLQUFBRDtJQUNBLCtFQUFtQztRQUNqQyxPQUFPRSxXQUFBO1FBQ1Q7SUFFQTtJQUNBLE1BQU1DLGVBQWEsSUFBQU4sNkJBQUFPLHVCQUFBO0lBRW5CLE1BQUFELFdBQUE7SUFDQSw0RUFBa0Q7SUFDbEQsa0RBQTBDRjtRQUN4Q0ksYUFBTUMscUJBQXdCQyxJQUFBQSxXQUFBQTtRQUM5QixNQUFLRCxXQUFNRSxhQUFrQkQscUJBQUE7YUFDM0IsTUFBS0QsT0FBU0UsVUFBTTtZQUNwQixLQUFBRixRQUFNRyxDQUFBQSxJQUFXSCxFQUFBQTtZQUdqQkgsTUFBQUEsV0FBaUJNLFFBQUFBLENBQUFBLElBQUFBLENBQUFBLEtBQUFBLENBQUFBLE1BQUFBLENBQUFBLENBQUFBLE9BQUFBLEtBQUFBLFFBQUFBLENBQUFBO1lBQ25CTixTQUFBTyxJQUFBLElBQUFEO1FBQ0Y7SUFFQTtRQUNFTixTQUFPUSxNQUFBO1FBQ1Q7SUFFQTtXQUVLUixXQUFBQSxHQUFBQSxDQUFBQSxHQUFTUyxZQUFLQyxHQUFBQSxFQUFBQSxZQUFBQSxRQUFBQSxFQUFBQTtrQkFDYlYsU0FBQVMsR0FBQSxFQUFBQzttQkFHaUIsa0JBQUFDLFlBQUFDLEdBQUE7Z0JBQ2JDLGFBQVk7Z0JBQ1pDLFlBQUk7Z0JBQ0pDLEtBQUFBO2dCQUNBQyxNQUFHZCxhQUFBZSxXQUFBLGVBQUFDLFVBQUFSO2dCQUxFQSxJQUFBQTtZQVFYLEdBQUFBOztJQUdOOztLQTFDMkJkIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvcHJlbG9hZC1jc3MudHN4P2RlNWMiXSwibmFtZXMiOlsiX3JlcXVlc3Rhc3luY3N0b3JhZ2VleHRlcm5hbCIsInJlcXVpcmUiLCJQcmVsb2FkQ3NzIiwicGFyYW0iLCJtb2R1bGVJZHMiLCJ3aW5kb3ciLCJhbGxGaWxlcyIsImdldEV4cGVjdGVkUmVxdWVzdFN0b3JlIiwicmVxdWVzdFN0b3JlIiwibWFuaWZlc3QiLCJyZWFjdExvYWRhYmxlTWFuaWZlc3QiLCJrZXkiLCJjc3NGaWxlcyIsInB1c2giLCJsZW5ndGgiLCJtYXAiLCJmaWxlIiwiX2pzeHJ1bnRpbWUiLCJqc3giLCJwcmVjZWRlbmNlIiwicmVsIiwiaHJlZiIsImFzIiwiYXNzZXRQcmVmaXgiLCJlbmNvZGVVUkkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js\n"));

/***/ })

});