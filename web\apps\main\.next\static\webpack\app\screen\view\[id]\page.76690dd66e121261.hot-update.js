"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/screen/components/modules/ServiceModule.tsx":
/*!*************************************************************!*\
  !*** ./src/app/screen/components/modules/ServiceModule.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ServiceModule; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ServiceModule() {\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalServices: 156,\n        onlineServices: 142,\n        todayApplications: 1247,\n        processingTime: 2.3,\n        satisfactionRate: 98.5,\n        popularServices: [\n            {\n                name: \"营业执照办理\",\n                applications: 234,\n                trend: \"+12%\"\n            },\n            {\n                name: \"户籍迁移\",\n                applications: 189,\n                trend: \"+8%\"\n            },\n            {\n                name: \"社保查询\",\n                applications: 156,\n                trend: \"+15%\"\n            },\n            {\n                name: \"税务申报\",\n                applications: 134,\n                trend: \"+5%\"\n            },\n            {\n                name: \"公积金提取\",\n                applications: 98,\n                trend: \"+22%\"\n            }\n        ],\n        recentActivity: [\n            {\n                time: \"14:32\",\n                action: \"营业执照审批完成\",\n                status: \"completed\"\n            },\n            {\n                time: \"14:28\",\n                action: \"户籍迁移申请提交\",\n                status: \"processing\"\n            },\n            {\n                time: \"14:25\",\n                action: \"社保变更审核中\",\n                status: \"processing\"\n            },\n            {\n                time: \"14:20\",\n                action: \"税务登记已完成\",\n                status: \"completed\"\n            },\n            {\n                time: \"14:15\",\n                action: \"公积金申请待审核\",\n                status: \"pending\"\n            }\n        ]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = ()=>{\n            setData({\n                totalServices: 156,\n                onlineServices: 142,\n                todayApplications: 1247,\n                processingTime: 2.3,\n                satisfactionRate: 98.5,\n                popularServices: [\n                    {\n                        name: \"营业执照办理\",\n                        applications: 234,\n                        trend: \"+12%\"\n                    },\n                    {\n                        name: \"户籍迁移\",\n                        applications: 189,\n                        trend: \"+8%\"\n                    },\n                    {\n                        name: \"社保查询\",\n                        applications: 156,\n                        trend: \"+15%\"\n                    },\n                    {\n                        name: \"税务申报\",\n                        applications: 134,\n                        trend: \"+5%\"\n                    },\n                    {\n                        name: \"公积金提取\",\n                        applications: 98,\n                        trend: \"+22%\"\n                    }\n                ],\n                recentActivity: [\n                    {\n                        time: \"14:32\",\n                        action: \"营业执照审批完成\",\n                        status: \"completed\"\n                    },\n                    {\n                        time: \"14:28\",\n                        action: \"户籍迁移申请提交\",\n                        status: \"processing\"\n                    },\n                    {\n                        time: \"14:25\",\n                        action: \"社保变更审核中\",\n                        status: \"processing\"\n                    },\n                    {\n                        time: \"14:20\",\n                        action: \"税务登记已完成\",\n                        status: \"completed\"\n                    },\n                    {\n                        time: \"14:15\",\n                        action: \"公积金申请待审核\",\n                        status: \"pending\"\n                    }\n                ]\n            });\n        };\n        const interval = setInterval(loadData, 30000);\n        return ()=>clearInterval(interval);\n    }, []);\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-3 h-3 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 16\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-3 h-3 text-yellow-400\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-orange-400\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gradient-to-br from-purple-800/20 to-purple-900/20 backdrop-blur-sm rounded-2xl border border-purple-700/30 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-5 h-5 text-purple-400\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white\",\n                        children: \"政务服务\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4 h-[calc(100%-60px)] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/30 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-white\",\n                                        children: data.totalServices\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: \"总服务数\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-purple-400\",\n                                        children: [\n                                            data.onlineServices,\n                                            \"项在线\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/30 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-white\",\n                                        children: data.todayApplications\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: \"今日申请\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-400\",\n                                        children: \"+15%\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-purple-300 mb-3\",\n                                children: \"服务指标\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300\",\n                                                children: \"平均办理时长\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-white\",\n                                                children: [\n                                                    data.processingTime,\n                                                    \"天\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300\",\n                                                children: \"满意度\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-green-400\",\n                                                children: [\n                                                    data.satisfactionRate,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-purple-300 mb-3\",\n                                children: \"热门服务\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: data.popularServices.slice(0, 4).map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300 truncate flex-1\",\n                                                children: service.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 ml-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-purple-300\",\n                                                        children: service.applications\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-400\",\n                                                        children: service.trend\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, service.name, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-purple-300 mb-3\",\n                                children: \"实时动态\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: data.recentActivity.slice(0, 4).map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400 w-10\",\n                                                children: activity.time\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            getStatusIcon(activity.status),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-300 truncate flex-1\",\n                                                children: activity.action\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\ServiceModule.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n_s(ServiceModule, \"3tehJWj/deZnyw9F1cKrJPi6jQ8=\");\n_c = ServiceModule;\nvar _c;\n$RefreshReg$(_c, \"ServiceModule\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/components/modules/ServiceModule.tsx\n"));

/***/ })

});