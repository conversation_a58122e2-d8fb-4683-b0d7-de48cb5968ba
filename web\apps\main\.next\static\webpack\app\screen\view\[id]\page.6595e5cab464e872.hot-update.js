"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/screen/components/modules/GeographicModule.tsx":
/*!****************************************************************!*\
  !*** ./src/app/screen/components/modules/GeographicModule.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GeographicModule; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Building_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Building,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Building,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building,MapPin,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GeographicModule() {\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        districts: [\n            {\n                name: \"中心区\",\n                population: 345678,\n                enterprises: 12456,\n                gdp: 456.7,\n                coordinates: {\n                    x: 50,\n                    y: 40\n                }\n            },\n            {\n                name: \"东城区\",\n                population: 234567,\n                enterprises: 8934,\n                gdp: 234.5,\n                coordinates: {\n                    x: 70,\n                    y: 35\n                }\n            },\n            {\n                name: \"西城区\",\n                population: 198765,\n                enterprises: 7823,\n                gdp: 198.3,\n                coordinates: {\n                    x: 30,\n                    y: 45\n                }\n            },\n            {\n                name: \"南区\",\n                population: 156789,\n                enterprises: 5678,\n                gdp: 167.8,\n                coordinates: {\n                    x: 45,\n                    y: 70\n                }\n            },\n            {\n                name: \"北区\",\n                population: 187654,\n                enterprises: 6789,\n                gdp: 189.2,\n                coordinates: {\n                    x: 55,\n                    y: 20\n                }\n            },\n            {\n                name: \"开发区\",\n                population: 123456,\n                enterprises: 9876,\n                gdp: 298.4,\n                coordinates: {\n                    x: 80,\n                    y: 60\n                }\n            }\n        ],\n        heatmapData: [\n            {\n                x: 25,\n                y: 30,\n                intensity: 0.8,\n                type: \"population\"\n            },\n            {\n                x: 45,\n                y: 25,\n                intensity: 0.9,\n                type: \"economic\"\n            },\n            {\n                x: 65,\n                y: 40,\n                intensity: 0.7,\n                type: \"service\"\n            },\n            {\n                x: 35,\n                y: 55,\n                intensity: 0.6,\n                type: \"population\"\n            },\n            {\n                x: 75,\n                y: 65,\n                intensity: 0.85,\n                type: \"economic\"\n            },\n            {\n                x: 55,\n                y: 75,\n                intensity: 0.65,\n                type: \"service\"\n            }\n        ]\n    });\n    const [selectedDistrict, setSelectedDistrict] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = ()=>{\n            setData({\n                districts: [\n                    {\n                        name: \"中心区\",\n                        population: 345678,\n                        enterprises: 12456,\n                        gdp: 456.7,\n                        coordinates: {\n                            x: 50,\n                            y: 40\n                        }\n                    },\n                    {\n                        name: \"东城区\",\n                        population: 234567,\n                        enterprises: 8934,\n                        gdp: 234.5,\n                        coordinates: {\n                            x: 70,\n                            y: 35\n                        }\n                    },\n                    {\n                        name: \"西城区\",\n                        population: 198765,\n                        enterprises: 7823,\n                        gdp: 198.3,\n                        coordinates: {\n                            x: 30,\n                            y: 45\n                        }\n                    },\n                    {\n                        name: \"南区\",\n                        population: 156789,\n                        enterprises: 5678,\n                        gdp: 167.8,\n                        coordinates: {\n                            x: 45,\n                            y: 70\n                        }\n                    },\n                    {\n                        name: \"北区\",\n                        population: 187654,\n                        enterprises: 6789,\n                        gdp: 189.2,\n                        coordinates: {\n                            x: 55,\n                            y: 20\n                        }\n                    },\n                    {\n                        name: \"开发区\",\n                        population: 123456,\n                        enterprises: 9876,\n                        gdp: 298.4,\n                        coordinates: {\n                            x: 80,\n                            y: 60\n                        }\n                    }\n                ],\n                heatmapData: [\n                    {\n                        x: 25,\n                        y: 30,\n                        intensity: 0.8,\n                        type: \"population\"\n                    },\n                    {\n                        x: 45,\n                        y: 25,\n                        intensity: 0.9,\n                        type: \"economic\"\n                    },\n                    {\n                        x: 65,\n                        y: 40,\n                        intensity: 0.7,\n                        type: \"service\"\n                    },\n                    {\n                        x: 35,\n                        y: 55,\n                        intensity: 0.6,\n                        type: \"population\"\n                    },\n                    {\n                        x: 75,\n                        y: 65,\n                        intensity: 0.85,\n                        type: \"economic\"\n                    },\n                    {\n                        x: 55,\n                        y: 75,\n                        intensity: 0.65,\n                        type: \"service\"\n                    }\n                ]\n            });\n        };\n        const interval = setInterval(loadData, 60000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gradient-to-br from-cyan-800/20 to-cyan-900/20 backdrop-blur-sm rounded-2xl border border-cyan-700/30 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-5 h-5 text-cyan-400\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white\",\n                        children: \"地理分布\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4 h-[calc(100%-60px)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2 bg-gray-800/30 rounded-lg p-3 relative overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-gray-900/50 to-gray-800/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-full h-full\",\n                                viewBox: \"0 0 100 100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M10,20 L90,20 L90,80 L10,80 Z\",\n                                        fill: \"none\",\n                                        stroke: \"rgba(99, 102, 241, 0.3)\",\n                                        strokeWidth: \"0.5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"50\",\n                                        y1: \"20\",\n                                        x2: \"50\",\n                                        y2: \"80\",\n                                        stroke: \"rgba(99, 102, 241, 0.2)\",\n                                        strokeWidth: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"10\",\n                                        y1: \"50\",\n                                        x2: \"90\",\n                                        y2: \"50\",\n                                        stroke: \"rgba(99, 102, 241, 0.2)\",\n                                        strokeWidth: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    data.heatmapData.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            cx: point.x,\n                                            cy: point.y,\n                                            r: point.intensity * 3,\n                                            fill: point.type === \"population\" ? \"rgba(59, 130, 246, 0.6)\" : point.type === \"economic\" ? \"rgba(34, 197, 94, 0.6)\" : \"rgba(168, 85, 247, 0.6)\",\n                                            className: \"animate-pulse\"\n                                        }, index, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)),\n                                    data.districts.map((district, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                    cx: district.coordinates.x,\n                                                    cy: district.coordinates.y,\n                                                    r: \"2\",\n                                                    fill: \"rgba(34, 211, 238, 0.8)\",\n                                                    className: \"cursor-pointer hover:r-3 transition-all\",\n                                                    onClick: ()=>setSelectedDistrict(district.name)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"text\", {\n                                                    x: district.coordinates.x,\n                                                    y: district.coordinates.y - 3,\n                                                    fontSize: \"2\",\n                                                    fill: \"white\",\n                                                    textAnchor: \"middle\",\n                                                    className: \"pointer-events-none\",\n                                                    children: district.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, district.name, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/30 rounded-lg p-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-semibold text-cyan-300 mb-2\",\n                                        children: \"图例\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-blue-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"人口密度\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"经济活跃度\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-purple-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"服务覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/30 rounded-lg p-3 flex-1 overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-semibold text-cyan-300 mb-2\",\n                                        children: \"区域统计\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: data.districts.map((district, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded cursor-pointer transition-all \".concat(selectedDistrict === district.name ? \"bg-cyan-600/30 border border-cyan-500/50\" : \"bg-gray-700/30 hover:bg-gray-700/50\"),\n                                                onClick: ()=>setSelectedDistrict(district.name),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-white mb-1\",\n                                                        children: district.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-1 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                                        lineNumber: 175,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: [\n                                                                            (district.population / 10000).toFixed(1),\n                                                                            \"万\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                                        lineNumber: 176,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        className: \"w-3 h-3 text-green-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300\",\n                                                                        children: district.enterprises\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-cyan-300 mt-1\",\n                                                        children: [\n                                                            \"GDP: \",\n                                                            district.gdp,\n                                                            \"亿\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, district.name, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\GeographicModule.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(GeographicModule, \"Z0oGtN01Yv+hFs2t9EQQDXRglWo=\");\n_c = GeographicModule;\nvar _c;\n$RefreshReg$(_c, \"GeographicModule\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/components/modules/GeographicModule.tsx\n"));

/***/ })

});