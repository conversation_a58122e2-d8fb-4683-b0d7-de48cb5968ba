"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/screen/components/modules/EconomicModule.tsx":
/*!**************************************************************!*\
  !*** ./src/app/screen/components/modules/EconomicModule.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EconomicModule; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction EconomicModule() {\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        gdp: {\n            current: 2456.8,\n            growth: 8.5\n        },\n        revenue: {\n            current: 345.6,\n            growth: 12.3\n        },\n        enterprises: {\n            total: 45623,\n            newThisMonth: 234\n        },\n        employment: {\n            rate: 96.8,\n            trend: 1.2\n        },\n        industryDistribution: [\n            {\n                name: \"制造业\",\n                percentage: 35.2,\n                color: \"from-blue-500 to-blue-600\"\n            },\n            {\n                name: \"服务业\",\n                percentage: 28.7,\n                color: \"from-green-500 to-green-600\"\n            },\n            {\n                name: \"科技业\",\n                percentage: 18.9,\n                color: \"from-purple-500 to-purple-600\"\n            },\n            {\n                name: \"农业\",\n                percentage: 10.4,\n                color: \"from-yellow-500 to-yellow-600\"\n            },\n            {\n                name: \"其他\",\n                percentage: 6.8,\n                color: \"from-gray-500 to-gray-600\"\n            }\n        ]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = ()=>{\n            setData({\n                gdp: {\n                    current: 2456.8,\n                    growth: 8.5\n                },\n                revenue: {\n                    current: 345.6,\n                    growth: 12.3\n                },\n                enterprises: {\n                    total: 45623,\n                    newThisMonth: 234\n                },\n                employment: {\n                    rate: 96.8,\n                    trend: 1.2\n                },\n                industryDistribution: [\n                    {\n                        name: \"制造业\",\n                        percentage: 35.2,\n                        color: \"from-blue-500 to-blue-600\"\n                    },\n                    {\n                        name: \"服务业\",\n                        percentage: 28.7,\n                        color: \"from-green-500 to-green-600\"\n                    },\n                    {\n                        name: \"科技业\",\n                        percentage: 18.9,\n                        color: \"from-purple-500 to-purple-600\"\n                    },\n                    {\n                        name: \"农业\",\n                        percentage: 10.4,\n                        color: \"from-yellow-500 to-yellow-600\"\n                    },\n                    {\n                        name: \"其他\",\n                        percentage: 6.8,\n                        color: \"from-gray-500 to-gray-600\"\n                    }\n                ]\n            });\n        };\n        const interval = setInterval(loadData, 60000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gradient-to-br from-green-800/20 to-green-900/20 backdrop-blur-sm rounded-2xl border border-green-700/30 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-5 h-5 text-green-400\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white\",\n                        children: \"经济指标\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4 h-[calc(100%-60px)] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/30 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 text-green-400 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-white\",\n                                        children: [\n                                            data.gdp.current,\n                                            \"亿\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: \"GDP总量\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-400\",\n                                        children: [\n                                            \"+\",\n                                            data.gdp.growth,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800/30 rounded-lg p-3 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-400 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-white\",\n                                        children: [\n                                            data.revenue.current,\n                                            \"亿\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: \"财政收入\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-400\",\n                                        children: [\n                                            \"+\",\n                                            data.revenue.growth,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-green-300 mb-3\",\n                                children: \"企业统计\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-300\",\n                                        children: \"注册企业总数\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-bold text-white\",\n                                        children: data.enterprises.total.toLocaleString()\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-300\",\n                                        children: \"本月新增\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-bold text-green-400\",\n                                        children: [\n                                            \"+\",\n                                            data.enterprises.newThisMonth\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-green-300 mb-3\",\n                                children: \"就业情况\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white mb-1\",\n                                        children: [\n                                            data.employment.rate,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-400 mb-2\",\n                                        children: \"就业率\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-400\",\n                                        children: [\n                                            \"较上月 +\",\n                                            data.employment.trend,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-green-300 mb-3\",\n                                children: \"产业分布\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: data.industryDistribution.map((industry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300\",\n                                                children: industry.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 flex-1 mx-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 bg-gray-700 rounded-full h-1.5\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r \".concat(industry.color, \" h-1.5 rounded-full transition-all duration-1000\"),\n                                                            style: {\n                                                                width: \"\".concat(industry.percentage, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-green-300 w-8\",\n                                                        children: [\n                                                            industry.percentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, industry.name, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\EconomicModule.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(EconomicModule, \"pS+LnHlrybaW4eu+nQlKvXFcYkQ=\");\n_c = EconomicModule;\nvar _c;\n$RefreshReg$(_c, \"EconomicModule\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/components/modules/EconomicModule.tsx\n"));

/***/ })

});