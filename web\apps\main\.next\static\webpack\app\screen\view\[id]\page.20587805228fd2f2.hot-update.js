"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/screen/components/modules/DataSummaryModule.tsx":
/*!*****************************************************************!*\
  !*** ./src/app/screen/components/modules/DataSummaryModule.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DataSummaryModule; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Building2,Database,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Building2,Database,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Building2,Database,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Building2,Database,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Building2,Database,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Building2,Database,FileText,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DataSummaryModule() {\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalPopulation: 1245678,\n        totalEnterprises: 45623,\n        totalServices: 156,\n        gdpGrowth: 8.5,\n        onlineServices: 142,\n        dataVolume: \"2.4TB\"\n    });\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模拟数据加载\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        const loadData = ()=>{\n            setData({\n                totalPopulation: 1245678,\n                totalEnterprises: 45623,\n                totalServices: 156,\n                gdpGrowth: 8.5,\n                onlineServices: 142,\n                dataVolume: \"2.4TB\"\n            });\n        };\n        const interval = setInterval(loadData, 30000) // 30秒更新一次\n        ;\n        return ()=>clearInterval(interval);\n    }, []);\n    const summaryItems = [\n        {\n            title: \"总人口\",\n            value: data.totalPopulation.toLocaleString(),\n            unit: \"人\",\n            icon: _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"from-blue-500 to-blue-600\",\n            trend: \"+2.3%\"\n        },\n        {\n            title: \"注册企业\",\n            value: data.totalEnterprises.toLocaleString(),\n            unit: \"家\",\n            icon: _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"from-green-500 to-green-600\",\n            trend: \"+5.7%\"\n        },\n        {\n            title: \"政务服务\",\n            value: data.totalServices.toString(),\n            unit: \"项\",\n            icon: _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"from-purple-500 to-purple-600\",\n            trend: \"+12\"\n        },\n        {\n            title: \"GDP增长\",\n            value: data.gdpGrowth.toString(),\n            unit: \"%\",\n            icon: _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"from-orange-500 to-orange-600\",\n            trend: \"+0.8%\"\n        },\n        {\n            title: \"在线服务\",\n            value: data.onlineServices.toString(),\n            unit: \"项\",\n            icon: _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"from-cyan-500 to-cyan-600\",\n            trend: \"+8\"\n        },\n        {\n            title: \"数据总量\",\n            value: data.dataVolume,\n            unit: \"\",\n            icon: _barrel_optimize_names_Activity_Building2_Database_FileText_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"from-pink-500 to-pink-600\",\n            trend: \"+15%\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gradient-to-br from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-2xl border border-gray-700/50 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-white\",\n                        children: \"数据概览\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"实时更新\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-6 gap-6 h-[calc(100%-80px)]\",\n                children: summaryItems.map((item, index)=>{\n                    const Icon = item.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-gray-800/60 to-gray-900/60 rounded-xl p-4 border border-gray-700/30 hover:border-gray-600/50 transition-all duration-300 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br \".concat(item.color, \" rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-green-400 font-medium\",\n                                        children: item.trend\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: [\n                                            item.value,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400 ml-1\",\n                                                children: item.unit\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, item.title, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\DataSummaryModule.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(DataSummaryModule, \"g7AVKF+S+rkD3aHBFAf8wfE/Xlg=\");\n_c = DataSummaryModule;\nvar _c;\n$RefreshReg$(_c, \"DataSummaryModule\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/components/modules/DataSummaryModule.tsx\n"));

/***/ })

});