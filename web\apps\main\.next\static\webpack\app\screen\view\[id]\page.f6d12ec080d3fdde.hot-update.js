"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/screen/view/[id]/page",{

/***/ "(app-pages-browser)/./src/app/screen/components/modules/TrendAnalysisModule.tsx":
/*!*******************************************************************!*\
  !*** ./src/app/screen/components/modules/TrendAnalysisModule.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TrendAnalysisModule; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Target,TrendingUp!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction TrendAnalysisModule() {\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        monthlyGrowth: [\n            {\n                month: \"1月\",\n                gdp: 6.2,\n                population: 1.8,\n                services: 12.5\n            },\n            {\n                month: \"2月\",\n                gdp: 6.8,\n                population: 2.1,\n                services: 15.2\n            },\n            {\n                month: \"3月\",\n                gdp: 7.2,\n                population: 2.3,\n                services: 18.7\n            },\n            {\n                month: \"4月\",\n                gdp: 7.8,\n                population: 2.5,\n                services: 22.1\n            },\n            {\n                month: \"5月\",\n                gdp: 8.1,\n                population: 2.7,\n                services: 25.8\n            },\n            {\n                month: \"6月\",\n                gdp: 8.5,\n                population: 2.9,\n                services: 28.3\n            }\n        ],\n        yearlyComparison: {\n            thisYear: 2456.8,\n            lastYear: 2267.3,\n            growth: 8.4\n        },\n        predictions: [\n            {\n                indicator: \"GDP增长率\",\n                current: 8.5,\n                predicted: 9.2,\n                confidence: 85\n            },\n            {\n                indicator: \"人口增长\",\n                current: 2.9,\n                predicted: 3.1,\n                confidence: 78\n            },\n            {\n                indicator: \"服务增长\",\n                current: 28.3,\n                predicted: 32.5,\n                confidence: 92\n            },\n            {\n                indicator: \"企业注册\",\n                current: 15.7,\n                predicted: 18.2,\n                confidence: 88\n            }\n        ]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadData = ()=>{\n            setData({\n                monthlyGrowth: [\n                    {\n                        month: \"1月\",\n                        gdp: 6.2,\n                        population: 1.8,\n                        services: 12.5\n                    },\n                    {\n                        month: \"2月\",\n                        gdp: 6.8,\n                        population: 2.1,\n                        services: 15.2\n                    },\n                    {\n                        month: \"3月\",\n                        gdp: 7.2,\n                        population: 2.3,\n                        services: 18.7\n                    },\n                    {\n                        month: \"4月\",\n                        gdp: 7.8,\n                        population: 2.5,\n                        services: 22.1\n                    },\n                    {\n                        month: \"5月\",\n                        gdp: 8.1,\n                        population: 2.7,\n                        services: 25.8\n                    },\n                    {\n                        month: \"6月\",\n                        gdp: 8.5,\n                        population: 2.9,\n                        services: 28.3\n                    }\n                ],\n                yearlyComparison: {\n                    thisYear: 2456.8,\n                    lastYear: 2267.3,\n                    growth: 8.4\n                },\n                predictions: [\n                    {\n                        indicator: \"GDP增长率\",\n                        current: 8.5,\n                        predicted: 9.2,\n                        confidence: 85\n                    },\n                    {\n                        indicator: \"人口增长\",\n                        current: 2.9,\n                        predicted: 3.1,\n                        confidence: 78\n                    },\n                    {\n                        indicator: \"服务增长\",\n                        current: 28.3,\n                        predicted: 32.5,\n                        confidence: 92\n                    },\n                    {\n                        indicator: \"企业注册\",\n                        current: 15.7,\n                        predicted: 18.2,\n                        confidence: 88\n                    }\n                ]\n            });\n        };\n        const interval = setInterval(loadData, 60000);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gradient-to-br from-orange-800/20 to-orange-900/20 backdrop-blur-sm rounded-2xl border border-orange-700/30 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-5 h-5 text-orange-400\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold text-white\",\n                        children: \"趋势分析\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4 h-[calc(100%-60px)] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-orange-300 mb-3\",\n                                children: \"年度对比\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300\",\n                                                children: \"今年GDP\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-white\",\n                                                children: [\n                                                    data.yearlyComparison.thisYear,\n                                                    \"亿\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300\",\n                                                children: \"去年同期\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    data.yearlyComparison.lastYear,\n                                                    \"亿\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-300\",\n                                                children: \"增长率\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-bold text-green-400\",\n                                                children: [\n                                                    \"+\",\n                                                    data.yearlyComparison.growth,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-orange-300 mb-3\",\n                                children: \"月度趋势\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-20 relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-full h-full\",\n                                            viewBox: \"0 0 100 50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                    points: \"10,40 25,35 40,30 55,25 70,20 85,15\",\n                                                    fill: \"none\",\n                                                    stroke: \"rgba(34, 197, 94, 0.8)\",\n                                                    strokeWidth: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                    points: \"10,45 25,42 40,39 55,36 70,33 85,30\",\n                                                    fill: \"none\",\n                                                    stroke: \"rgba(59, 130, 246, 0.8)\",\n                                                    strokeWidth: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                    points: \"10,35 25,28 40,22 55,18 70,12 85,8\",\n                                                    fill: \"none\",\n                                                    stroke: \"rgba(168, 85, 247, 0.8)\",\n                                                    strokeWidth: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"GDP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"人口\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: \"服务\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-orange-300 mb-3\",\n                                children: \"预测分析\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: data.predictions.map((prediction, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-300\",\n                                                        children: prediction.indicator\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-orange-300\",\n                                                        children: [\n                                                            prediction.predicted,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 bg-gray-700 rounded-full h-1.5\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-orange-500 to-orange-400 h-1.5 rounded-full transition-all duration-1000\",\n                                                            style: {\n                                                                width: \"\".concat(prediction.confidence, \"%\")\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400 w-8\",\n                                                        children: [\n                                                            prediction.confidence,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, prediction.indicator, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-800/30 rounded-lg p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-orange-300 mb-3\",\n                                children: \"关键指标\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4 text-green-400 mx-auto mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white font-bold\",\n                                                children: \"+8.5%\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400\",\n                                                children: \"增长率\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Target_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 text-blue-400 mx-auto mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white font-bold\",\n                                                children: \"92%\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400\",\n                                                children: \"目标达成\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\screen\\\\components\\\\modules\\\\TrendAnalysisModule.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(TrendAnalysisModule, \"ftB45hOejG3OTkkN0lnLdBIOHl8=\");\n_c = TrendAnalysisModule;\nvar _c;\n$RefreshReg$(_c, \"TrendAnalysisModule\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/screen/components/modules/TrendAnalysisModule.tsx\n"));

/***/ })

});